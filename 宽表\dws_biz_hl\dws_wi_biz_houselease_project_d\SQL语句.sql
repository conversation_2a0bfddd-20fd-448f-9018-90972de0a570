SELECT
    d.project_id,
    d.project_name,
    d.asset_cate,
    d.project_status,
    d.is_listing,
    d.is_dealed,
    d.prj_manager,
    d.prj_department,
    d.prj_department_claim,
    d.lessee_member,
    d.lessor_member,
    d.is_state_lease,
    d.is_mandatory_entry,
    d.is_priority_right,
    d.priority_lessee_nm,
    d.is_operate,
    d.usage_house as usage_require,
    d.usage_state,
    d.is_pre_list,
    d.margin_money,
    d.intend_lessee_cnt,
    d.pick_method,
    d.online_bid_type,
    d.is_esign,
    d.location_cnt_house,
    d.province_cnt_house,
    d.city_cnt_house,
    d.district_cnt_house,
    d.street_cnt_house,
    d.biz_district_cnt_house,
    d.area_sum_house,
    d.land_right_cnt_house,
    d.asset_class_cnt_house,
    d.province_montage_house,
    d.city_montage_house,
    d.district_montage_house,
    d.street_montage_house,
    d.biz_district_montage_house,
    d.land_right_montage_house,
    d.asset_class_montage_house,
    d.is_land,
    d.location_cnt_land,
    d.area_sum_land,
    d.is_machine,
    d.machine_nm,
    d.is_vehicle,
    d.is_aircraft,
    d.vehicle_num,
    d.is_other_asset,
    d.other_asset_nm,
    m.basic_info_house,  -- 关联 MAP 字段
    m.basic_info_land,   -- 关联 MAP 字段
    d.list_start_date,
    d.list_end_date,
    d.list_start_year,
    d.list_end_year,
    d.list_start_ym,
    d.list_end_ym,
    d.annual_days,
    d.monthly_days,
    d.is_sep_price,
    d.list_price_uni,
    d.list_price_unit_uni,
    m.list_price_sep,    -- 关联 MAP 字段
    d.list_price_std_sq_d,
    d.list_price_std_sq_m,
    d.list_price_std_sq_y,
    d.list_price_std_d,
    d.list_price_std_m,
    d.list_price_std_y,
    d.list_price_sys_m,
    d.list_price_sys_y,
    d.list_price_sys_d,
    d.list_price_sys_std_sq_d,
    d.list_price_sys_std_sq_m,
    d.list_price_sys_std_sq_y,
    d.lease_area,
    d.lease_prd_type,
    d.lease_prd_range_type,
    d.lease_prd_ent1,
    d.lease_prd_ent2,
    d.lease_prd_end_date,
    d.lease_prd_std_d,
    d.lease_prd_std_m,
    d.lease_prd_std_y,
    d.list_total_price_cal,
    d.list_total_price_sys,
    d.is_sep_appraise,
    d.appr_price_uni,
    d.appr_price_unit_uni,
    m.appr_price_sep,    -- 关联 MAP 字段
    d.appr_price_std_sq_d,
    d.appr_total_price,
    d.re_list_num,
    d.is_last_list,
    d.f_list_start_date,
    d.f_list_end_date,
    d.f_list_start_year,
    d.f_list_end_year,
    d.f_list_start_ym,
    d.f_list_end_ym,
    d.f_list_price_std_sq_d,
    d.reduce_price_std_sq_d,
    d.is_extend,
    d.free_prd_type,
    d.free_prd_ent1,
    d.free_prd_ent2,
    d.free_prd_std_d,
    d.deal_date,
    d.deal_price_type,
    d.deal_price_uni,
    d.deal_price_unit_uni,
    d.deal_price_remark,
    m.deal_price_sep,    -- 关联 MAP 字段
    d.deal_price_std_sq_d,
    d.deal_total_price,
    d.deal_lease_area,
    d.deal_lease_prd_ent,
    d.deal_lease_prd_std_d,
    d.premium_vs_list_price,
    d.premium_vs_list_total_price,
    d.premium_rate_vs_list_price,
    d.premium_vs_appr_price,
    d.premium_vs_appr_total_price,
    d.premium_rate_vs_appr_price,
    d.deal_price_precontract,
    d.increase_vs_precontract,
    d.increase_rate_vs_precontract,
    d.actual_deal_methond,
    d.bid_type,
    d.sign_date_contract,
    d.effective_date_contract,
    d.lessor_name,
    d.lessor_province,
    d.lessor_city,
    d.lessor_district,
    d.lessor_reg_addr,
    d.lessor_type_sys,
    d.lessor_type_research,
    d.lessor_type_municipal,
    d.asset_source_l2,
    d.asset_source_l3,
    d.asset_source_l4,
    d.asset_source_l5,
    d.parent_group,
    d.approval_unit,
    d.enterprise_tier_num,
    d.asset_source_ent_l1,
    d.asset_source_ent_l2,
    d.asset_source_ent_l3,
    d.asset_source_ent_l4,
    d.asset_source_ent_l5,
    d.lessor_eco_nature,
    d.lessor_industry_type,
    d.lessor_industry,
    d.lessor_biz_scope,
    d.reg_intend_lessee_cnt,
    d.mm_intend_lessee_cnt,
    d.bid_intend_lessee_cnt,
    d.lessee_name,
    d.lessee_type,
    d.lessee_province,
    d.lessee_district,
    d.lessee_name_second_bid,
    d.lessee_offer_second_bid,
    d.lessee_name_third_bid,
    d.lessee_offer_third_bid,
    d.total_revenue,
    d.net_revenue,
    d.lessor_service_fee,
    d.lessee_service_fee,
    d.lessor_memb_comm,
    d.lessee_memb_comm,
    d.intend_lessee_mmb_comm,
    d.remain_pay_method,
    d.is_foreign_curr_settle,
    d.is_mm_handle_changed,
    d.mm_handle_method,
    d.is_mm_convert_rent,
    d.prj_entry_date,
    d.ulti_lessee_confirm_date,
    d.deal_cycle_ent_list_nd,
    d.deal_cycle_list_conf_nd,
    d.deal_cycle_conf_deal_nd,
    d.deal_cycle_ent_deal_nd,
    d.deal_cycle_list_deal_nd,
    d.deal_cycle_ent_list_wd,
    d.deal_cycle_list_conf_wd,
    d.deal_cycle_conf_deal_wd,
    d.deal_cycle_ent_deal_wd,
    d.deal_cycle_list_deal_wd,
    d.prj_view_cnt,
    d.prj_follow_cnt,
    d.price_adjust_method,
    d.list_price_notes,
    d.rent_mm_pay_require,
    d.is_rent_cover_other_fees,
    d.lessee_resp_fees,
    d.no_intend_lessee_opt,
    d.is_improve_allowed,
    d.is_intend_lessee_biz_inv,
    d.is_appraise_disclosed,
    d.other_list_items,
    d.annual_days_sys,
    d.monthly_days_sys
FROM
    (
           SELECT DISTINCT
        project_id,
        project_name,
        asset_cate,
        project_status,
        is_listing,
        is_dealed,
        prj_manager,
        prj_department,
        prj_department_claim,
        lessee_member,
        lessor_member,
        is_state_lease,
        is_mandatory_entry,
        is_priority_right,
        priority_lessee_nm,
        is_operate,
        usage_house,
        usage_state,
        is_pre_list,
        margin_money,
        intend_lessee_cnt,
        pick_method,
        online_bid_type,
        is_esign,
        location_cnt_house,
        province_cnt_house,
        city_cnt_house,
        district_cnt_house,
        street_cnt_house,
        biz_district_cnt_house,
        area_sum_house,
        land_right_cnt_house,
        asset_class_cnt_house,
        province_montage_house,
        city_montage_house,
        district_montage_house,
        street_montage_house,
        biz_district_montage_house,
        land_right_montage_house,
        asset_class_montage_house,
        is_land,
        location_cnt_land,
        area_sum_land,
        is_machine,
        machine_nm,
        is_vehicle,
        is_aircraft,
        vehicle_num,
        is_other_asset,
        other_asset_nm,
        list_start_date,
        list_end_date,
        list_start_year,
        list_end_year,
        list_start_ym,
        list_end_ym,
        annual_days,
        monthly_days,
        is_sep_price,
        list_price_uni,
        list_price_unit_uni,
        list_price_std_sq_d,
        list_price_std_sq_m,
        list_price_std_sq_y,
        list_price_std_d,
        list_price_std_m,
        list_price_std_y,
        list_price_sys_m,
        list_price_sys_y,
        list_price_sys_d,
        list_price_sys_std_sq_d,
        list_price_sys_std_sq_m,
        list_price_sys_std_sq_y,
        lease_area,
        lease_prd_type,
        lease_prd_range_type,
        lease_prd_ent1,
        lease_prd_ent2,
        lease_prd_end_date,
        lease_prd_std_d,
        lease_prd_std_m,
        lease_prd_std_y,
        list_total_price_cal,
        list_total_price_sys,
        is_sep_appraise,
        appr_price_uni,
        appr_price_unit_uni,
        appr_price_std_sq_d,
        appr_total_price,
        re_list_num,
        is_last_list,
        f_list_start_date,
        f_list_end_date,
        f_list_start_year,
        f_list_end_year,
        f_list_start_ym,
        f_list_end_ym,
        f_list_price_std_sq_d,
        reduce_price_std_sq_d,
        is_extend,
        free_prd_type,
        free_prd_ent1,
        free_prd_ent2,
        free_prd_std_d,
        deal_date,
        deal_price_type,
        deal_price_uni,
        deal_price_unit_uni,
        deal_price_remark,
        deal_price_std_sq_d,
        deal_total_price,
        deal_lease_area,
        deal_lease_prd_ent,
        deal_lease_prd_std_d,
        premium_vs_list_price,
        premium_vs_list_total_price,
        premium_rate_vs_list_price,
        premium_vs_appr_price,
        premium_vs_appr_total_price,
        premium_rate_vs_appr_price,
        deal_price_precontract,
        increase_vs_precontract,
        increase_rate_vs_precontract,
        actual_deal_methond,
        bid_type,
        sign_date_contract,
        effective_date_contract,
        lessor_name,
        lessor_province,
        lessor_city,
        lessor_district,
        lessor_reg_addr,
        lessor_type_sys,
        lessor_type_research,
        lessor_type_municipal,
        asset_source_l2,
        asset_source_l3,
        asset_source_l4,
        asset_source_l5,
        parent_group,
        approval_unit,
        enterprise_tier_num,
        asset_source_ent_l1,
        asset_source_ent_l2,
        asset_source_ent_l3,
        asset_source_ent_l4,
        asset_source_ent_l5,
        lessor_eco_nature,
        lessor_industry_type,
        lessor_industry,
        lessor_biz_scope,
        reg_intend_lessee_cnt,
        mm_intend_lessee_cnt,
        bid_intend_lessee_cnt,
        lessee_name,
        lessee_type,
        lessee_province,
        lessee_district,
        lessee_name_second_bid,
        lessee_offer_second_bid,
        lessee_name_third_bid,
        lessee_offer_third_bid,
        total_revenue,
        net_revenue,
        lessor_service_fee,
        lessee_service_fee,
        lessor_memb_comm,
        lessee_memb_comm,
        intend_lessee_mmb_comm,
        remain_pay_method,
        is_foreign_curr_settle,
        is_mm_handle_changed,
        mm_handle_method,
        is_mm_convert_rent,
        prj_entry_date,
        ulti_lessee_confirm_date,
        deal_cycle_ent_list_nd,
        deal_cycle_list_conf_nd,
        deal_cycle_conf_deal_nd,
        deal_cycle_ent_deal_nd,
        deal_cycle_list_deal_nd,
        deal_cycle_ent_list_wd,
        deal_cycle_list_conf_wd,
        deal_cycle_conf_deal_wd,
        deal_cycle_ent_deal_wd,
        deal_cycle_list_deal_wd,
        prj_view_cnt,
        prj_follow_cnt,
        price_adjust_method,
        list_price_notes,
        rent_mm_pay_require,
        is_rent_cover_other_fees,
        lessee_resp_fees,
        no_intend_lessee_opt,
        is_improve_allowed,
        is_intend_lessee_biz_inv,
        is_appraise_disclosed,
        other_list_items,
        annual_days_sys,
        monthly_days_sys
    FROM
        table1
    ) d
LEFT JOIN
    (
        SELECT
        project_id,
        basic_info_house,
        basic_info_land,
        list_price_sep,
        appr_price_sep,
        deal_price_sep
    FROM
        table1
    ) m
ON
    d.project_id = m.project_id